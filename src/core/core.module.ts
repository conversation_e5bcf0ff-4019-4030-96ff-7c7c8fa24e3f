import { ApolloServerPluginInlineTrace } from '@apollo/server/plugin/inlineTrace';
import { ApolloFederationDriver, ApolloFederationDriverConfig } from '@nestjs/apollo';
import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_GUARD, Reflector } from '@nestjs/core';
import { DiscoveryModule } from '@nestjs/core';
import { CqrsModule } from '@nestjs/cqrs';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { GraphQLModule } from '@nestjs/graphql';
import { EMAIL_SERVICE_EXCHANGE } from '@skillspace/amqp-contracts';
import {
    AmqpBrokerModule,
    AuthModule,
    GlobalExceptionModule,
    GlobalInterceptorModule,
    JwtAuthGqlGuard,
    LoggerModule,
    OpentelemetryModule,
    PermissionsGuard,
    RabbitMQExchangeConfig,
} from '@skillspace/lib';

import { appConfig, NODE_ENV } from '../config/app.config';
import { HealthModule } from './health/health.module';
import { PrismaModule } from './prisma/prisma.module';

const AMQP_CONFIG_EXCHANGES: RabbitMQExchangeConfig[] = [EMAIL_SERVICE_EXCHANGE];

@Global()
@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
            load: [() => ({ app: appConfig })],
        }),
        AuthModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: async (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>('JWT_SECRET'),
            }),
        }),
        GraphQLModule.forRoot<ApolloFederationDriverConfig>({
            driver: ApolloFederationDriver,
            playground: appConfig.nodeEnv !== NODE_ENV.PROD,
            autoSchemaFile: true,
            formatError: (error) => {
                return {
                    message: error.message,
                    path: error.path,
                };
            },
            plugins: appConfig.nodeEnv === NODE_ENV.TEST ? [ApolloServerPluginInlineTrace()] : [],
        }),
        AmqpBrokerModule.forRootAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: async (configService: ConfigService) => {
                return {
                    exchanges: AMQP_CONFIG_EXCHANGES,
                    mainExchange: EMAIL_SERVICE_EXCHANGE,
                    uri: configService.get<string>('RMQ_URL'),
                };
            },
        }),
        AmqpBrokerModule,
        CqrsModule,
        DiscoveryModule,
        GlobalInterceptorModule,
        GlobalExceptionModule,
        HealthModule,
        PrismaModule,
        LoggerModule.forRoot(),
        OpentelemetryModule.forRoot(),
        EventEmitterModule.forRoot(),
    ],
    providers: [
        {
            provide: APP_GUARD,
            useClass: JwtAuthGqlGuard,
        },
        {
            provide: APP_GUARD,
            useClass: PermissionsGuard,
        },
        Reflector,
    ],
    exports: [AmqpBrokerModule, AuthModule, PrismaModule, CqrsModule],
})
export class CoreModule {}
