import { EmailServiceSendEmailToManyContractNamespace } from '@skillspace/amqp-contracts';

import { ISmtpProvider } from '../../src/emails/domain/infrastructure/smtp-provider.interface';
import { SMTP_PROVIDER } from '../../src/emails/injects';
import { EmailConsumer } from '../../src/emails/presentation/email.consumer';
import { DEFAULT_SMTP, SCHOOL_WITH_SMTP_UUID } from './test-constants';
import { employeeSession01, prisma } from './test-setup';
import { app } from './test-setup';

const formatAmqMessage = (
    payload: EmailServiceSendEmailToManyContractNamespace.RequestPayload,
): EmailServiceSendEmailToManyContractNamespace.Message => ({
    requestUuid: '202b67c2-5aee-4e44-abb3-b66b0747ea64',
    timestamp: Date.now(),
    payload,
});

const customSmtpSettings = {
    useCustomSmtp: true,
    host: 'custom.smtp.server.com',
    port: 587,
    user: '<EMAIL>',
    password: 'customPassword123',
    email: '<EMAIL>',
};

const messagePayload = {
    recipients: ['<EMAIL>'],
    subject: 'Test Custom SMTP Settings',
    body: {},
    template: 'test_smtp',
    schoolUuid: SCHOOL_WITH_SMTP_UUID,
} as EmailServiceSendEmailToManyContractNamespace.RequestPayload;

const defaultOptions = {
    host: 'host.test',
    port: 25,
    secure: false,
    auth: { user: 'user/test', pass: '12345' },
};

const customOptions = {
    host: 'custom.smtp.server.com',
    port: 587,
    secure: false,
    auth: { user: '<EMAIL>', pass: 'customPassword123' },
};

describe('Custom SMTP Settings Integration Test', () => {
    let smtpProvider: ISmtpProvider;
    let emailConsumer: EmailConsumer;
    let sendEmailSpy: jest.SpyInstance;

    beforeAll(async () => {
        smtpProvider = app.get<ISmtpProvider>(SMTP_PROVIDER);
        emailConsumer = app.get<EmailConsumer>(EmailConsumer);
    });

    beforeEach(async () => {
        sendEmailSpy = jest.spyOn(smtpProvider, 'sendEmail');
    });

    afterEach(async () => {
        jest.clearAllMocks();
    });

    it('Должен создать кастомные настройки SMTP, применить их, проверить в БД, отключить и использовать дефолтные', async () => {
        // 1. Создаём кастомные настройки SMTP для школы через GraphQL
        const createResult = await employeeSession01.updateSmtpSettings(customSmtpSettings);

        // Проверяем, что настройки созданы, но useCustomSmtp автоматически установлен в false
        expect(createResult.updateSmtpSettings).toEqual({
            ...customSmtpSettings,
            useCustomSmtp: false, // Автоматически отключается при создании/изменении
        });

        // 2. Применяем настройки (включаем useCustomSmtp: true)
        const enableResult = await employeeSession01.updateSmtpSettings({ useCustomSmtp: true });

        expect(enableResult.updateSmtpSettings).toEqual({
            ...customSmtpSettings,
            useCustomSmtp: true,
        });

        // 3. Проверяем в БД, что useCustomSmtp действительно true
        const dbRecord = await prisma.smtpSettings.findUnique({
            where: { schoolUuid: SCHOOL_WITH_SMTP_UUID },
        });

        expect(dbRecord).not.toBeNull();
        expect(dbRecord!.useCustomSmtp).toBe(true);
        expect(dbRecord!.host).toBe(customSmtpSettings.host);
        expect(dbRecord!.port).toBe(customSmtpSettings.port);
        expect(dbRecord!.user).toBe(customSmtpSettings.user);
        expect(dbRecord!.password).toBe(customSmtpSettings.password);
        expect(dbRecord!.email).toBe(customSmtpSettings.email);

        // Отправляем письмо с кастомными настройками
        await emailConsumer.sendEmailToMany(formatAmqMessage(messagePayload));

        // Проверяем, что используются кастомные настройки SMTP
        expect(sendEmailSpy).toHaveBeenCalledWith(
            customOptions,
            expect.objectContaining({
                subject: 'Test Custom SMTP Settings',
                to: ['<EMAIL>'],
                from: '<EMAIL>',
            }),
        );

        // Очищаем моки для следующей части теста
        jest.clearAllMocks();

        // 4. Отключаем кастомные настройки
        const disableResult = await employeeSession01.updateSmtpSettings({ useCustomSmtp: false });

        expect(disableResult.updateSmtpSettings.useCustomSmtp).toBe(false);

        const disabledResponse = await employeeSession01.getSmtpSettings();
        expect(disabledResponse.smtpSettings.useCustomSmtp).toBe(false);

        // Проверяем в БД, что useCustomSmtp теперь false
        const dbRecordAfterDisable = await prisma.smtpSettings.findUnique({
            where: { schoolUuid: SCHOOL_WITH_SMTP_UUID },
        });

        expect(dbRecordAfterDisable!.useCustomSmtp).toBe(false);

        // 5. Отправляем письмо и убеждаемся, что используются дефолтные настройки
        await emailConsumer.sendEmailToMany(formatAmqMessage(messagePayload));

        // Проверяем, что используются дефолтные настройки SMTP
        expect(sendEmailSpy).toHaveBeenCalledWith(
            defaultOptions,
            expect.objectContaining({
                subject: 'Test Custom SMTP Settings',
                to: ['<EMAIL>'],
                from: DEFAULT_SMTP.email,
            }),
        );
    });

    it('Должен использовать дефолтные настройки при отсутствии кастомных настроек в БД', async () => {
        // Удаляем все настройки SMTP из БД
        await prisma.smtpSettings.deleteMany({
            where: { schoolUuid: SCHOOL_WITH_SMTP_UUID },
        });

        // Отправляем письмо
        await emailConsumer.sendEmailToMany(formatAmqMessage(messagePayload));

        // Проверяем, что используются дефолтные настройки
        expect(sendEmailSpy).toHaveBeenCalledWith(
            defaultOptions,
            expect.objectContaining({
                subject: 'Test Custom SMTP Settings',
                to: ['<EMAIL>'],
                from: DEFAULT_SMTP.email,
            }),
        );
    });

    it('Должен автоматически отключать кастомные настройки при изменении параметров SMTP', async () => {
        // Сначала создаём и включаем кастомные настройки
        await employeeSession01.updateSmtpSettings(customSmtpSettings);
        await employeeSession01.updateSmtpSettings({ useCustomSmtp: true });

        // Проверяем, что настройки включены
        let dbRecord = await prisma.smtpSettings.findUnique({
            where: { schoolUuid: SCHOOL_WITH_SMTP_UUID },
        });
        expect(dbRecord!.useCustomSmtp).toBe(true);

        // Изменяем любой параметр SMTP (например, host)
        const updateResult = await employeeSession01.updateSmtpSettings({
            host: 'new.smtp.server.com',
        });

        // Проверяем, что useCustomSmtp автоматически отключился
        expect(updateResult.updateSmtpSettings.useCustomSmtp).toBe(false);

        // Проверяем в БД
        dbRecord = await prisma.smtpSettings.findUnique({
            where: { schoolUuid: SCHOOL_WITH_SMTP_UUID },
        });
        expect(dbRecord!.useCustomSmtp).toBe(false);
        expect(dbRecord!.host).toBe('new.smtp.server.com');
    });
});
