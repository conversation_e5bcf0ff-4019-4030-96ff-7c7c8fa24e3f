import { ISmtpProvider, Newsletter } from '../../../src/emails/domain/infrastructure/smtp-provider.interface';
import { SmtpTargetOptions } from '../../../src/emails/domain/models/smtp-settings/smtp-settings.abstract';

export class SmtpProviderMock implements ISmtpProvider {
    async sendEmail(options: SmtpTargetOptions, newsLetter: Newsletter): Promise<void> {
        // Сохраняем параметры для проверки в тестах
        return;
    }

    async verifySmtpSettings(options: SmtpTargetOptions): Promise<void> {
        // Сохраняем параметры для проверки в тестах
        return;
    }
}
